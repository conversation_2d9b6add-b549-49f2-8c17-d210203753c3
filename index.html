<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NCast - Podcast App</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #ffffff;
            color: #1f1f1f;
            max-width: 428px;
            margin: 0 auto;
            min-height: 100vh;
            position: relative;
        }

        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 24px 32px;
        }

        .logo-container {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .logo {
            width: 50px;
            height: 46px;
        }

        .logo-text {
            font-size: 24px;
            font-weight: bold;
            color: #4c0099;
        }

        .notification-container {
            position: relative;
        }

        .notification-btn {
            width: 48px;
            height: 48px;
            background-color: rgba(31, 31, 31, 0.1);
            border-radius: 24px;
            border: none;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
        }

        .notification-badge {
            position: absolute;
            top: 2px;
            right: 4px;
            width: 12px;
            height: 12px;
            background-color: #ff5757;
            border-radius: 6px;
        }

        .title {
            font-size: 24px;
            font-weight: 600;
            margin: 0 32px 28px 32px;
        }

        .podcast-list {
            padding: 0 32px;
            margin-bottom: 100px;
        }

        .podcast-item {
            display: flex;
            align-items: center;
            gap: 16px;
            margin-bottom: 24px;
        }

        .podcast-image {
            width: 108px;
            height: 96px;
            border-radius: 16px;
            object-fit: cover;
        }

        .podcast-info {
            flex: 1;
        }

        .podcast-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 8px;
        }

        .podcast-category {
            font-size: 14px;
            color: rgba(31, 31, 31, 0.7);
            margin-bottom: 8px;
        }

        .podcast-duration {
            font-size: 14px;
            color: rgba(31, 31, 31, 0.7);
        }

        .play-btn {
            width: 48px;
            height: 48px;
            background-color: rgba(76, 0, 153, 0.1);
            border-radius: 24px;
            border: none;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
        }

        .play-icon {
            width: 18px;
            height: 18px;
            background-color: #4c0099;
            clip-path: polygon(0 0, 100% 50%, 0 100%);
        }

        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 428px;
            height: 156px;
            background: linear-gradient(180deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 1) 100%);
            display: flex;
            align-items: flex-end;
            justify-content: center;
            padding-bottom: 32px;
        }

        .nav-container {
            width: 364px;
            height: 72px;
            background-color: rgba(76, 0, 153, 0.1);
            border-radius: 48px;
            display: flex;
            align-items: center;
            justify-content: space-around;
            position: relative;
        }

        .nav-icon {
            width: 32px;
            height: 32px;
            opacity: 0.5;
            cursor: pointer;
        }

        .nav-icon.active {
            opacity: 1;
        }

        .nav-dot {
            position: absolute;
            bottom: 16px;
            left: 50%;
            transform: translateX(-50%);
            width: 5px;
            height: 5px;
            background-color: #4c0099;
            border-radius: 50%;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="logo-container">
            <img src="assets/logo.svg" alt="NCast Logo" class="logo">
            <span class="logo-text">NCAST</span>
        </div>
        <div class="notification-container">
            <button class="notification-btn">
                <img src="assets/bell-icon.svg" alt="Notifications" width="21" height="21">
            </button>
            <div class="notification-badge"></div>
        </div>
    </div>

    <h1 class="title">Favourite Podcasts</h1>

    <div class="podcast-list">
        <div class="podcast-item">
            <img src="assets/podcast1.png" alt="Sunday Summer - Ep3" class="podcast-image">
            <div class="podcast-info">
                <div class="podcast-title">Sunday Summer - Ep3</div>
                <div class="podcast-category">Entertainment</div>
                <div class="podcast-duration">15 min</div>
            </div>
            <button class="play-btn">
                <div class="play-icon"></div>
            </button>
        </div>

        <div class="podcast-item">
            <img src="assets/podcast2.png" alt="Musical Soul - Vol. 1" class="podcast-image">
            <div class="podcast-info">
                <div class="podcast-title">Musical Soul - Vol. 1</div>
                <div class="podcast-category">Lifestyle</div>
                <div class="podcast-duration">35 min</div>
            </div>
            <button class="play-btn">
                <div class="play-icon"></div>
            </button>
        </div>

        <div class="podcast-item">
            <img src="assets/podcast3.png" alt="Talk Show - Ep4" class="podcast-image">
            <div class="podcast-info">
                <div class="podcast-title">Talk Show - Ep4</div>
                <div class="podcast-category">Business</div>
                <div class="podcast-duration">20 min</div>
            </div>
            <button class="play-btn">
                <div class="play-icon"></div>
            </button>
        </div>

        <div class="podcast-item">
            <img src="assets/podcast4.png" alt="Musical Soul - Vol. 2" class="podcast-image">
            <div class="podcast-info">
                <div class="podcast-title">Musical Soul - Vol. 2</div>
                <div class="podcast-category">Lifestyle</div>
                <div class="podcast-duration">30 min</div>
            </div>
            <button class="play-btn">
                <div class="play-icon"></div>
            </button>
        </div>

        <div class="podcast-item">
            <img src="assets/podcast5.png" alt="Unravelling The Mind" class="podcast-image">
            <div class="podcast-info">
                <div class="podcast-title">Unravelling The Mind</div>
                <div class="podcast-category">Healthy Lifestyle</div>
                <div class="podcast-duration">10 min</div>
            </div>
            <button class="play-btn">
                <div class="play-icon"></div>
            </button>
        </div>

        <div class="podcast-item">
            <img src="assets/podcast6.png" alt="Talk Show - Ep8" class="podcast-image">
            <div class="podcast-info">
                <div class="podcast-title">Talk Show - Ep8</div>
                <div class="podcast-category">Entertainment</div>
                <div class="podcast-duration">20 min</div>
            </div>
            <button class="play-btn">
                <div class="play-icon"></div>
            </button>
        </div>
    </div>

    <div class="bottom-nav">
        <div class="nav-container">
            <img src="assets/headphones-icon.svg" alt="Headphones" class="nav-icon">
            <img src="assets/compass-icon.svg" alt="Compass" class="nav-icon">
            <img src="assets/heart-icon.svg" alt="Heart" class="nav-icon active">
            <img src="assets/profile-icon.svg" alt="Profile" class="nav-icon">
            <div class="nav-dot"></div>
        </div>
    </div>
</body>
</html>
